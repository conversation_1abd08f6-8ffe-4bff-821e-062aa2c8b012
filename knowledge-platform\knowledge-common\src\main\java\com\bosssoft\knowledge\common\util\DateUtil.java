package com.bosssoft.knowledge.common.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 日期工具类
 */
public class DateUtil {

    /**
     * 日期格式 yyyy-MM-dd
     */
    public static final String PATTERN_DATE = "yyyy-MM-dd";
    
    /**
     * 日期时间格式 yyyy-MM-dd HH:mm:ss
     */
    public static final String PATTERN_DATETIME = "yyyy-MM-dd HH:mm:ss";
    
    /**
     * 时间格式 HH:mm:ss
     */
    public static final String PATTERN_TIME = "HH:mm:ss";

    /**
     * 获取当前日期时间
     *
     * @return 当前时间
     */
    public static Date now() {
        return new Date();
    }

    /**
     * 获取当前日期的字符串，格式为 yyyy-MM-dd
     *
     * @return 当前日期的字符串
     */
    public static String todayString() {
        return format(now(), PATTERN_DATE);
    }

    /**
     * 获取当前日期时间的字符串，格式为 yyyy-MM-dd HH:mm:ss
     *
     * @return 当前日期时间的字符串
     */
    public static String nowString() {
        return format(now(), PATTERN_DATETIME);
    }

    /**
     * 将日期对象格式化为字符串
     *
     * @param date    日期对象
     * @param pattern 格式
     * @return 格式化后的字符串
     */
    public static String format(Date date, String pattern) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    /**
     * 将字符串解析为日期对象
     *
     * @param dateStr 日期字符串
     * @param pattern 格式
     * @return 日期对象
     */
    public static Date parse(String dateStr, String pattern) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException("日期解析异常: " + dateStr, e);
        }
    }

    /**
     * 计算两个日期之间相差的天数
     *
     * @param date1 日期1
     * @param date2 日期2
     * @return 相差的天数
     */
    public static long daysBetween(Date date1, Date date2) {
        LocalDate localDate1 = dateToLocalDate(date1);
        LocalDate localDate2 = dateToLocalDate(date2);
        return Math.abs(localDate1.toEpochDay() - localDate2.toEpochDay());
    }

    /**
     * Date 转 LocalDate
     *
     * @param date 日期
     * @return LocalDate
     */
    public static LocalDate dateToLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * Date 转 LocalDateTime
     *
     * @param date 日期
     * @return LocalDateTime
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * LocalDate 转 Date
     *
     * @param localDate 本地日期
     * @return Date
     */
    public static Date localDateToDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * LocalDateTime 转 Date
     *
     * @param localDateTime 本地日期时间
     * @return Date
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 日期增加天数
     *
     * @param date 日期
     * @param days 天数
     * @return 增加后的日期
     */
    public static Date addDays(Date date, int days) {
        LocalDateTime localDateTime = dateToLocalDateTime(date);
        LocalDateTime newLocalDateTime = localDateTime.plusDays(days);
        return localDateTimeToDate(newLocalDateTime);
    }

    /**
     * 日期减少天数
     *
     * @param date 日期
     * @param days 天数
     * @return 减少后的日期
     */
    public static Date minusDays(Date date, int days) {
        return addDays(date, -days);
    }

    /**
     * 获取指定日期的开始时间（00:00:00）
     *
     * @param date 日期
     * @return 该日期的开始时间
     */
    public static Date startOfDay(Date date) {
        LocalDate localDate = dateToLocalDate(date);
        LocalDateTime startOfDay = localDate.atStartOfDay();
        return localDateTimeToDate(startOfDay);
    }

    /**
     * 获取指定日期的结束时间（23:59:59）
     *
     * @param date 日期
     * @return 该日期的结束时间
     */
    public static Date endOfDay(Date date) {
        LocalDate localDate = dateToLocalDate(date);
        LocalDateTime endOfDay = LocalDateTime.of(localDate, LocalTime.of(23, 59, 59));
        return localDateTimeToDate(endOfDay);
    }

    /**
     * 判断是否为同一天
     *
     * @param date1 日期1
     * @param date2 日期2
     * @return 是否为同一天
     */
    public static boolean isSameDay(Date date1, Date date2) {
        LocalDate localDate1 = dateToLocalDate(date1);
        LocalDate localDate2 = dateToLocalDate(date2);
        return localDate1.isEqual(localDate2);
    }
} 