package com.bosssoft.knowledge.common.result;

/**
 * 返回结果状态码
 */
public enum ResultCode {
    /**
     * 成功
     */
    SUCCESS(200, "操作成功"),
    
    /**
     * 失败
     */
    ERROR(500, "操作失败"),
    
    /**
     * 参数验证失败
     */
    VALIDATE_FAILED(400, "参数检验失败"),
    
    /**
     * 未登录或登录已过期
     */
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    
    /**
     * 没有权限
     */
    FORBIDDEN(403, "没有相关权限");
    
    private final Integer code;
    private final String message;
    
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
} 