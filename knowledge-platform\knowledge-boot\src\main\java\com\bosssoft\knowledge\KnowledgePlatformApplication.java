package com.bosssoft.knowledge;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 知识库管理平台启动类
 * 
 * 采用多模块结构，但只有一个启动类
 * 通过ComponentScan扫描所有模块的组件
 * 通过MapperScan扫描所有模块的Mapper接口
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.bosssoft.knowledge"})
@MapperScan(basePackages = {"com.bosssoft.knowledge.*.mapper"})
public class KnowledgePlatformApplication {
    public static void main(String[] args) {
        SpringApplication.run(KnowledgePlatformApplication.class, args);
    }
}
