# 知识库管理平台

## 项目介绍

知识库管理平台是一个用于创建、管理和使用结构化知识内容的系统。该平台支持多种内容导入方式，提供知识库的组织管理、内容维护、权限控制等功能，实现企业知识资产的有效管理和利用。

## 系统功能

- 支持批量或者单个新增、修改、删除、查看、编辑知识库
- 支持通过文件上传、网页链接内容导入、文本直接导入等方式添加知识
- 支持文件关联附件功能
- 支持群组权限管理，下级群组对上级群组有只读权限
- 支持知识库文件自动切片与人工复核
- 支持为知识切片添加问题，丰富知识库问答能力

## 技术架构

- **开发环境**：Java 1.8、Maven 3.6+
- **核心框架**：Spring Boot 2.7.18
- **ORM框架**：MyBatis-Plus 3.5.3
- **数据库**：MySQL 8.0.33
- **API文档**：Knife4j 4.0.0

## 项目结构

```
knowledge-platform/                    # 父项目
├── knowledge-boot/                    # 启动模块，包含唯一启动类
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/                  # 包含启动类
│   │   │   └── resources/             # 配置文件
│   │   └── test/                      # 测试代码
│   └── pom.xml                        # 启动模块POM
├── knowledge-common/                  # 公共模块
│   ├── src/
│   │   └── main/
│   │       └── java/                  # 公共组件、工具类、通用配置等
│   └── pom.xml                        # 公共模块POM
├── knowledge-base/                    # 知识库管理模块
│   ├── src/
│   │   └── main/
│   │       └── java/                  # 知识库核心管理功能
│   └── pom.xml                        # 知识库管理模块POM
├── knowledge-file/                    # 文件管理模块
│   ├── src/
│   │   └── main/
│   │       └── java/                  # 文件管理相关功能
│   └── pom.xml                        # 文件管理模块POM
├── knowledge-slice/                   # 知识切片模块
│   ├── src/
│   │   └── main/
│   │       └── java/                  # 知识切片和问题管理功能
│   └── pom.xml                        # 知识切片模块POM
├── knowledge-user/                    # 用户与群组模块
│   ├── src/
│   │   └── main/
│   │       └── java/                  # 用户和权限管理功能
│   └── pom.xml                        # 用户与群组模块POM
└── pom.xml                            # 父POM，管理依赖版本
```

## 模块介绍

- **knowledge-boot**：包含应用启动类和全局配置，整合所有其他功能模块
- **knowledge-common**：公共组件、工具类、通用配置、常量定义等
- **knowledge-base**：知识库核心管理功能
- **knowledge-file**：文件管理相关功能
- **knowledge-slice**：知识切片和问题管理功能
- **knowledge-user**：用户和权限管理功能

## 快速开始

### 环境准备
1. JDK 1.8+
2. Maven 3.6+
3. MySQL 8.0+

### 编译构建
```bash
# 编译整个项目
mvn clean package -DskipTests
```

### 启动应用
```bash
# 运行启动模块的JAR包
java -jar knowledge-boot/target/knowledge-boot-1.0.0-SNAPSHOT.jar
```

## 部署说明
1. 配置数据库连接：修改 `knowledge-boot/src/main/resources/application.yml` 中的数据库连接参数
2. 配置文件存储路径：修改 `knowledge-boot/src/main/resources/application.yml` 中的文件存储路径配置

## 开发指南
请参考各模块下的README.md文件了解详细开发说明。 